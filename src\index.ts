import Fastify from 'fastify';
import { env, isDevelopment } from './shared/config/env';
import logger from './shared/logger/logger';
import { securityMiddleware } from './shared/middleware/security.middleware';
import { defaultMetricsMiddleware } from './shared/middleware/metrics.middleware';
import { errorHandler } from './shared/middleware/error-handler.middleware';


const fastify = Fastify({
  logger: isDevelopment
    ? {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
          },
        },
        serializers: {
          req(request) {
            // Não logar requisições para endpoints de monitoramento
            if (
              request.url === '/metrics' ||
              request.url?.startsWith('/health')
            ) {
              return {};
            }
            return {
              method: request.method,
              url: request.url,
              hostname: request.hostname,
              remoteAddress: request.ip,
              remotePort: request.socket?.remotePort,
            };
          },
        },
      }
    : {
        serializers: {
          req(request) {
            // Não logar requisições para endpoints de monitoramento em produção também
            if (
              request.url === '/metrics' ||
              request.url?.startsWith('/health')
            ) {
              return {};
            }
            return {
              method: request.method,
              url: request.url,
              hostname: request.hostname,
              remoteAddress: request.ip,
            };
          },
        },
      },
});

fastify.addHook(
  'preHandler',
  securityMiddleware({
    enableSqlInjectionProtection: true,
    enableXssProtection: true,
    enablePathTraversalProtection: true,
    enableInputSanitization: true,
    strictMode: false,
    logAttempts: true,
  })
);

// Adicionar hooks de métricas
fastify.addHook('onRequest', defaultMetricsMiddleware.onRequest);
fastify.addHook('onResponse', defaultMetricsMiddleware.onResponse);
fastify.addHook('onError', defaultMetricsMiddleware.onError);

fastify.setErrorHandler(errorHandler);